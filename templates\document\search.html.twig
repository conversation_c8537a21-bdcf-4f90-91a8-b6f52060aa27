{% extends 'base.html.twig' %}

{% block title %}Rechercher des documents{% endblock %}

{% block body %}
    <div class="container mt-5">
        <h1>Rechercher des documents</h1>

        <form method="get" action="{{ path('document_search') }}">
            <div class="form-group">
                <label for="search">Rechercher par contenu :</label>
                <input type="text" id="search" name="q" class="form-control" placeholder="Entrez un mot-clé...">
            </div>

            <button type="submit" class="btn btn-primary">Rechercher</button>
        </form>

        {% if documents is defined %}
            <div class="mt-4">
                <h2>Résultats de la recherche</h2>

                {% if documents|length > 0 %}
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Date</th>
                                <th>Contenu OCR</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for document in documents %}
                                <tr>
                                    <td>{{ document.name }}</td>
                                    <td>{{ document.date|date('d/m/Y') }}</td>
                                    <td>{{ document.ocrContent|slice(0, 100) }}...</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% else %}
                    <p>Aucun document trouvé.</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
{% endblock %}
