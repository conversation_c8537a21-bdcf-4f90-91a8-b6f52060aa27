# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides
#
# Real environment variables win over .env files.
#
# DO NOT DEFINE PRODUCTION SECRETS IN THIS FILE NOR IN ANY OTHER COMMITTED FILES.
#
# Run "composer dump-env prod" to compile .env files for production use (requires symfony/flex >=1.2).
# https://symfony.com/doc/current/configuration/secrets.html

###> symfony/framework-bundle ###
APP_ENV=dev
APP_SECRET=your_secret_key_here
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
# Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url
# IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml
#
DATABASE_URL="****************************************/document_archive?serverVersion=16&charset=utf8"
###< doctrine/doctrine-bundle ###

###> Google Cloud Vision API ###
# Path to your Google Cloud service account JSON file
GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
###< Google Cloud Vision API ###

###> OAuth2 Google ###
OAUTH_GOOGLE_CLIENT_ID=your_google_client_id
OAUTH_GOOGLE_CLIENT_SECRET=your_google_client_secret
###< OAuth2 Google ###

###> symfony/mailer ###
MAILER_DSN=null://null
###< symfony/mailer ###
