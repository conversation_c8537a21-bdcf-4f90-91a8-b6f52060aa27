version: '3.8'

services:
  php-fpm:
    build:
      context: .
      dockerfile: .docker/Dockerfile
    volumes:
      - .:/var/www/html
    depends_on:
      - database

  nginx:
    image: nginx:latest
    ports:
      - "8080:80"
    volumes:
      - .:/var/www/html
      - .docker/nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php-fpm

  database:
    image: postgres:16-alpine
    environment:
      POSTGRES_DB: document_archive
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - database_data:/var/lib/postgresql/data

volumes:
  database_data:
