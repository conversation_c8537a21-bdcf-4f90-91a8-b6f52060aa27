<?php

namespace App\Service;

use Google\Cloud\Vision\V1\ImageAnnotatorClient;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class GoogleVisionService
{
    private string $googleCredentials;

    public function __construct(string $googleCredentials)
    {
        $this->googleCredentials = $googleCredentials;
    }

    public function extractText(UploadedFile $file): string
    {
        try {
            $imageAnnotator = new ImageAnnotatorClient([
                'credentials' => $this->googleCredentials,
            ]);

            $imageContent = file_get_contents($file->getPathname());
            if ($imageContent === false) {
                throw new \RuntimeException('Unable to read file content');
            }

            $response = $imageAnnotator->textDetection($imageContent);
            $error = $response->getError();

            if ($error) {
                throw new \RuntimeException('Google Vision API error: ' . $error->getMessage());
            }

            $annotation = $response->getTextAnnotations();

            if ($annotation && count($annotation) > 0) {
                return $annotation[0]->getDescription();
            }

            return '';
        } catch (\Exception $e) {
            // Log the error for debugging
            error_log('OCR extraction failed: ' . $e->getMessage());
            return '';
        }
    }
}
