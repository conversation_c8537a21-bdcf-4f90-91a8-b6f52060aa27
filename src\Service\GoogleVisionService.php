<?php

namespace App\Service;

use Google\Cloud\Vision\V1\ImageAnnotatorClient;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class GoogleVisionService
{
    private string $googleCredentials;

    public function __construct(string $googleCredentials)
    {
        $this->googleCredentials = $googleCredentials;
    }

    public function extractText(UploadedFile $file): string
    {
        $imageAnnotator = new ImageAnnotatorClient([
            'credentials' => $this->googleCredentials,
        ]);

        $imageContent = file_get_contents($file->getPathname());
        $response = $imageAnnotator->textDetection($imageContent);
        $annotation = $response->getTextAnnotations();

        if ($annotation) {
            return $annotation[0]->getDescription();
        }

        return '';
    }
}
