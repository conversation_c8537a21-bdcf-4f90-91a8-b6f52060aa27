{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.1", "ext-ctype": "*", "ext-iconv": "*", "doctrine/doctrine-bundle": "2.13.2", "doctrine/orm": "^3.3", "knpuniversity/oauth2-client-bundle": "^2.18", "league/flysystem-bundle": "^3.4", "league/oauth2-google": "^4.0", "symfony/asset": "6.4.*", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/flex": "^2.5", "symfony/form": "6.4.*", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/mailer": "6.4.*", "symfony/monolog-bundle": "^3.10", "symfony/runtime": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/uid": "6.4.*", "symfony/validator": "6.4.*", "symfony/webpack-encore-bundle": "^2.2", "symfony/yaml": "6.4.*", "twig/extra-bundle": "^3.20"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": true, "require": "6.4.*"}}}