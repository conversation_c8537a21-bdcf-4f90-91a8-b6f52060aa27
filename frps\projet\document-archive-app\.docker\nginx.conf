server {
    listen 80;
    server_name localhost;

    location / {
        # Redirect all requests to the Symfony front controller
        try_files $uri /index.php$is_args$args;
    }

    location ~ ^/index\.php {
        include fastcgi_params;
        fastcgi_pass php-fpm:9000; # Utiliser le nom de service correct
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    }

    location ~ \.php$ {
        return 404; # Prevent direct access to PHP files
    }

    location ~ /\.ht {
        deny all; # Deny access to .htaccess files
    }
}
