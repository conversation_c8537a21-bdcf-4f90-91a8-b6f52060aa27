/* Styles pour le tableau de bord */
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 3rem;
}

.welcome-message {
    font-size: 2.5rem;
    color: #2c3e50;
}

.user-email {
    color: #7f8c8d;
    font-size: 1.2rem;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: #ffffff;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.stat-card i {
    font-size: 2rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
}

.quick-actions {
    margin-bottom: 3rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.action-buttons .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.recent-documents {
    background: #ffffff;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.document-list {
    margin-top: 1rem;
}

.document-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
}

.document-item:last-child {
    border-bottom: none;
}

.document-item i {
    font-size: 1.5rem;
    color: #3498db;
}

.document-info h4 {
    margin: 0;
    color: #2c3e50;
}

.document-info p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.no-documents {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
}
