<?php

namespace App\Controller;

use App\Entity\Document;
use App\Form\DocumentUploadType;
use App\Repository\DocumentRepository;
use App\Service\GoogleVisionService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DocumentController extends AbstractController
{
    #[Route('/document/upload', name: 'document_upload')]
    public function upload(Request $request, EntityManagerInterface $entityManager, GoogleVisionService $googleVisionService): Response
    {
        $document = new Document();
        $form = $this->createForm(DocumentUploadType::class, $document);

        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $file = $form->get('file')->getData();

            if (!$file) {
                $this->addFlash('error', 'No file was uploaded.');
                return $this->render('document/upload.html.twig', [
                    'form' => $form->createView(),
                ]);
            }

            // Validate file type and size
            $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
            if (!in_array($file->getMimeType(), $allowedMimeTypes)) {
                $this->addFlash('error', 'Invalid file type. Only JPEG, PNG, GIF, and PDF files are allowed.');
                return $this->render('document/upload.html.twig', [
                    'form' => $form->createView(),
                ]);
            }

            $maxFileSize = 10 * 1024 * 1024; // 10MB
            if ($file->getSize() > $maxFileSize) {
                $this->addFlash('error', 'File size too large. Maximum size is 10MB.');
                return $this->render('document/upload.html.twig', [
                    'form' => $form->createView(),
                ]);
            }

            try {
                $ocrContent = $googleVisionService->extractText($file);

                $document->setName($file->getClientOriginalName());
                $document->setDate(new \DateTimeImmutable());
                $document->setOcrContent($ocrContent);

                $entityManager->persist($document);
                $entityManager->flush();

                $this->addFlash('success', 'Document uploaded successfully!');
                return $this->redirectToRoute('dashboard');
            } catch (\Exception $e) {
                $this->addFlash('error', 'Failed to process document: ' . $e->getMessage());
                return $this->render('document/upload.html.twig', [
                    'form' => $form->createView(),
                ]);
            }
        }

        return $this->render('document/upload.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/document/search', name: 'document_search')]
    public function search(Request $request, DocumentRepository $documentRepository): Response
    {
        $query = $request->query->get('q');
        $documents = [];

        if ($query) {
            $documents = $documentRepository->searchByContent($query);
        }

        return $this->render('document/search.html.twig', [
            'documents' => $documents,
            'query' => $query,
        ]);
    }
}
