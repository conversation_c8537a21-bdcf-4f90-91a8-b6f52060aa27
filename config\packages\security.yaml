security:
    password_hashers:
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'
        App\Entity\User: 'auto'

    providers:
        app_user_provider:
            id: App\Security\UserProvider

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        main:
            pattern: ^/
            lazy: true
            provider: app_user_provider
            custom_authenticators:
                - App\Security\OAuthUserProvider
            logout:
                path: /logout
                target: /login

    access_control:
        - { path: ^/login, roles: PUBLIC_ACCESS }
        - { path: ^/oauth, roles: PUBLIC_ACCESS }
        - { path: ^/dashboard, roles: ROLE_USER }

when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
